// Standardized reaction display utilities for the platform

export const REACTION_EMOJIS: Record<string, string> = {
  'like': '👍',
  'love': '❤️',
  'fire': '🔥',
  'laugh': '😂',
  'smile': '😊',
  'wow': '😮',
  'sad': '😢',
  'cry': '😢',
  'angry': '😠',
  'broken': '💔'
}

export interface ReactionData {
  id: string
  reaction_type: string
  user?: {
    name: string
    avatar?: string
    profile_picture_url?: string
  }
}

export interface ReactionDisplayResult {
  emoji: string
  totalCount: number
  reactionCounts: Record<string, number>
  mostPopularType: string
}

/**
 * Standardized function to display reactions across the platform
 * Shows the most popular reaction emoji with total count
 */
export function getReactionDisplay(reactions: ReactionData[]): ReactionDisplayResult {
  if (!reactions || reactions.length === 0) {
    return {
      emoji: '❤️',
      totalCount: 0,
      reactionCounts: {},
      mostPopularType: 'love'
    }
  }

  // Count reactions by type
  const reactionCounts = reactions.reduce((acc: Record<string, number>, reaction) => {
    acc[reaction.reaction_type] = (acc[reaction.reaction_type] || 0) + 1
    return acc
  }, {})

  // Find the most popular reaction type
  const mostPopularType = Object.entries(reactionCounts).reduce((a, b) => a[1] > b[1] ? a : b)[0]
  
  // Get the emoji for the most popular reaction
  const emoji = REACTION_EMOJIS[mostPopularType] || '❤️'

  return {
    emoji,
    totalCount: reactions.length,
    reactionCounts,
    mostPopularType
  }
}

/**
 * Component for displaying reaction count with most popular emoji
 */
export function ReactionDisplay({ 
  reactions, 
  onClick, 
  className = "cursor-pointer hover:underline" 
}: { 
  reactions: ReactionData[]
  onClick?: () => void
  className?: string 
}) {
  const { emoji, totalCount } = getReactionDisplay(reactions)

  if (totalCount === 0) {
    return null
  }

  return (
    <span 
      className={className} 
      onClick={onClick}
      title="View reactions"
    >
      {emoji} {totalCount}
    </span>
  )
}
