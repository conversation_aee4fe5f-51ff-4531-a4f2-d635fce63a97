import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

// GET - Fetch current user's audio posts for dashboard
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's audio posts with complete reaction and reply data
    const { data: audioPosts, error: audioPostsError } = await supabase
      .from("audio_posts")
      .select(`
        id,
        audio_url,
        description,
        duration_seconds,
        love_count,
        reply_count,
        play_count,
        created_at,
        audio_reactions:audio_reactions(
          id,
          reaction_type,
          user:users(name, avatar, profile_picture_url)
        ),
        audio_replies:audio_replies(
          id,
          audio_url,
          duration_seconds,
          love_count,
          created_at,
          user:users(name, avatar, profile_picture_url)
        )
      `)
      .eq("user_id", user.id)
      .order("created_at", { ascending: false })

    if (audioPostsError) {
      console.error('Error fetching user audio posts:', audioPostsError)
      return NextResponse.json({ error: 'Failed to fetch audio posts' }, { status: 500 })
    }

    return NextResponse.json({ audioPosts: audioPosts || [] })
  } catch (error) {
    console.error('User audio posts API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
